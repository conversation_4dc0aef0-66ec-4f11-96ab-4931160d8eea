import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { CompressionRouter, CompressionMethod } from './compression-router';
import { analytics, UploadMetrics } from './ec2-analytics';

// Environment configuration
const getEnvironmentDirectories = () => {
  const nodeEnv = process.env.NODE_ENV || 'development';

  switch (nodeEnv) {
    case 'production':
      return {
        uploadDir: process.env.EC2_UPLOAD_DIR || '/mnt/data/uploads',
        workDir: process.env.EC2_WORK_DIR || '/mnt/data/work'
      };
    case 'test':
      return {
        uploadDir: path.join(__dirname, '../tests/test-files/uploads'),
        workDir: path.join(__dirname, '../tests/test-files/work')
      };
    case 'development':
    default:
      return {
        uploadDir: process.env.UPLOAD_DIR || './uploads',
        workDir: process.env.WORK_DIR || './work'
      };
  }
};

const { uploadDir: UPLOAD_DIR, workDir: WORK_DIR } = getEnvironmentDirectories();
const SCRIPTS_DIR = process.env.SCRIPTS_DIR || path.join(__dirname, '../../../scripts');

// Dynamic file size limit getter for testing flexibility
const getMaxFileSize = () => {
  return parseInt(process.env.MAX_FILE_SIZE || '107374182400'); // 100GB in bytes
};

// AWS S3 Configuration
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY ? {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  } : undefined,
  requestHandler: {
    requestTimeout: 1800000, // 30 minutes for large file operations
    connectionTimeout: 60000, // 1 minute connection timeout
  },
});

const COMPRESSED_BUCKET = process.env.COMPRESSED_BUCKET || 'fasttransfer-compressed-dev';

// Ensure directories exist
const ensureDirectories = () => {
  [UPLOAD_DIR, WORK_DIR].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });
};

// Configure multer for local file storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    ensureDirectories();
    cb(null, UPLOAD_DIR);
  },
  filename: (req, file, cb) => {
    // Use provided transferId from form data, or generate new one
    const transferId = (req as any).body?.transferId || uuidv4();
    const timestamp = Date.now();
    const filename = `${timestamp}-${transferId}-${file.originalname}`;
    cb(null, filename);
  }
});

// Create multer upload function that uses dynamic file size limit
const createUpload = () => {
  return multer({
    storage,
    limits: {
      fileSize: getMaxFileSize(),
      files: 1
    },
    fileFilter: (req, file, cb) => {
      // Basic file type validation - can be expanded
      const allowedMimes = [
        'text/plain', 'application/pdf', 'application/vnd.ms-excel',
        'application/msword', 'image/vnd.adobe.photoshop', 'text/csv',
        'application/octet-stream', 'application/dicom', 'application/vnd.ms-powerpoint',
        'video/mp4', 'video/avi', 'video/quicktime', 'audio/mpeg', 'audio/wav',
        'image/tiff', 'image/jpeg', 'image/png'
      ];

      // Allow all files for now, but log the type
      console.log(`📄 File type: ${file.mimetype} for ${file.originalname}`);
      cb(null, true);
    }
  });
};

interface CompressionResult {
  success: boolean;
  compressedPath?: string;
  originalSize: number;
  compressedSize?: number;
  compressionRatio?: number;
  compressionTime?: number;
  error?: string;
}

interface UploadResult {
  transferId: string;
  originalFilename: string;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  compressionTime: number;
  s3Key: string;
  downloadUrl: string;
  expiresAt: number;
}

class EC2UploadService {
  private compressionRouter: CompressionRouter;

  constructor() {
    this.compressionRouter = new CompressionRouter(SCRIPTS_DIR);
    ensureDirectories();
  }

  /**
   * Get the appropriate file extension for compressed files
   * Video, audio, and image files keep their original extensions
   * Other files get .zmt extension
   */
  private getCompressedFileExtension(originalFilename: string): string {
    const ext = path.extname(originalFilename).toLowerCase();

    // Video extensions
    const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.y4m'];

    // Audio extensions
    const audioExtensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'];

    // Image extensions
    const imageExtensions = ['.tiff', '.tif', '.jpg', '.jpeg', '.png', '.gif', '.bmp'];

    // DICOM extensions
    const dicomExtensions = ['.dcm', '.dicom'];

    // If it's video, audio, image, or DICOM, keep original extension
    if (videoExtensions.includes(ext) || audioExtensions.includes(ext) ||
        imageExtensions.includes(ext) || dicomExtensions.includes(ext)) {
      return ext;
    }

    // Otherwise use .zmt extension
    return '.zmt';
  }

  async processUpload(filePath: string, originalFilename: string, providedTransferId?: string): Promise<UploadResult> {
    const startTime = Date.now();
    const transferId = providedTransferId || uuidv4();
    const originalSize = fs.statSync(filePath).size;
    const fileExtension = path.extname(originalFilename).toLowerCase();

    console.log(`🚀 Processing upload: ${originalFilename} (${originalSize} bytes)`);

    let uploadMetrics: UploadMetrics = {
      transferId,
      timestamp: startTime,
      originalFilename,
      originalSize,
      compressedSize: 0,
      compressionRatio: 0,
      compressionTime: 0,
      uploadTime: 0,
      totalProcessingTime: 0,
      compressionMethod: '',
      fileExtension,
      uploadSpeed: 0,
      success: false
    };

    try {
      // Step 1: Compress the file
      const compressionResult = await this.compressFile(filePath, originalFilename, transferId);

      if (!compressionResult.success || !compressionResult.compressedPath) {
        throw new Error(compressionResult.error || 'Compression failed');
      }

      // Update metrics with compression results
      uploadMetrics.compressedSize = compressionResult.compressedSize || 0;
      uploadMetrics.compressionRatio = compressionResult.compressionRatio || 0;
      uploadMetrics.compressionTime = compressionResult.compressionTime || 0;
      uploadMetrics.compressionMethod = (compressionResult as any).method || 'unknown';

      // Step 2: Upload compressed file to S3
      const s3UploadStart = Date.now();
      const compressedExtension = this.getCompressedFileExtension(originalFilename);
      const baseFilename = path.basename(originalFilename, path.extname(originalFilename));
      const s3Key = `compressed/${transferId}/${baseFilename}${compressedExtension}`;

      await this.uploadToS3(compressionResult.compressedPath, s3Key);

      const s3UploadTime = Date.now() - s3UploadStart;
      uploadMetrics.uploadTime = s3UploadTime;
      uploadMetrics.uploadSpeed = (uploadMetrics.compressedSize / 1024 / 1024) / (s3UploadTime / 1000); // MB/s

      // Step 3: Generate download URL
      const downloadUrl = await this.generateDownloadUrl(s3Key);

      // Step 4: Cleanup local files and work directory
      const workDir = path.join(WORK_DIR, transferId);
      await this.cleanup([filePath, compressionResult.compressedPath]);
      await this.cleanupDirectory(workDir);

      const totalTime = Date.now() - startTime;
      uploadMetrics.totalProcessingTime = totalTime;
      uploadMetrics.success = true;

      // Log analytics
      await analytics.logUploadMetrics(uploadMetrics);

      console.log(`✅ Upload processing completed in ${totalTime}ms`);

      return {
        transferId,
        originalFilename,
        originalSize,
        compressedSize: compressionResult.compressedSize || 0,
        compressionRatio: compressionResult.compressionRatio || 0,
        compressionTime: compressionResult.compressionTime || 0,
        s3Key,
        downloadUrl,
        expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
      };

    } catch (error) {
      console.error('❌ Upload processing failed:', error);

      // Update metrics with error
      uploadMetrics.totalProcessingTime = Date.now() - startTime;
      uploadMetrics.success = false;
      uploadMetrics.errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Log failed upload metrics
      await analytics.logUploadMetrics(uploadMetrics);

      // Cleanup on error
      const workDir = path.join(WORK_DIR, transferId);
      await this.cleanup([filePath]);
      await this.cleanupDirectory(workDir);
      throw error;
    }
  }

  private async compressFile(filePath: string, originalFilename: string, transferId: string): Promise<CompressionResult> {
    const startTime = Date.now();
    const workDir = path.join(WORK_DIR, transferId);
    const compressedExtension = this.getCompressedFileExtension(originalFilename);
    const baseFilename = path.basename(originalFilename, path.extname(originalFilename));
    const outputPath = path.join(workDir, `${baseFilename}${compressedExtension}`);

    try {
      // Create work directory
      if (!fs.existsSync(workDir)) {
        fs.mkdirSync(workDir, { recursive: true });
      }

      console.log(`🗜️ Starting compression: ${originalFilename}`);

      // Copy file to work directory for compression (overwrite if exists)
      const workFilePath = path.join(workDir, originalFilename);
      if (fs.existsSync(workFilePath)) {
        fs.unlinkSync(workFilePath); // Remove existing file first
      }
      fs.copyFileSync(filePath, workFilePath);
      console.log(`📁 Copied file to work directory: ${workFilePath}`);

      // Determine compression method based on file extension
      const method = this.compressionRouter.getCompressionMethod(originalFilename);
      console.log(`📋 Using compression method: ${method}`);

      // Use compression router to compress the file (now in work directory)
      const compressionResult = await this.compressionRouter.compressFiles([workFilePath], outputPath, method);

      // Store compression method for analytics
      (compressionResult as any).method = method;

      console.log(`✅ Compression completed: ${compressionResult.compressionRatio.toFixed(2)}% reduction in ${compressionResult.compressionTime}ms`);

      return {
        success: true,
        compressedPath: compressionResult.compressedPath,
        originalSize: compressionResult.originalSize,
        compressedSize: compressionResult.compressedSize,
        compressionRatio: compressionResult.compressionRatio,
        compressionTime: compressionResult.compressionTime
      };

    } catch (error) {
      console.error('❌ Compression failed:', error);
      return {
        success: false,
        originalSize: fs.statSync(filePath).size,
        error: error instanceof Error ? error.message : 'Unknown compression error'
      };
    }
  }

  private async uploadToS3(filePath: string, s3Key: string): Promise<void> {
    const startTime = Date.now();
    const fileSize = fs.statSync(filePath).size;
    console.log(`☁️ Uploading to S3: ${s3Key} (${fileSize} bytes)`);

    try {
      const fileStream = fs.createReadStream(filePath);
      const uploadCommand = new PutObjectCommand({
        Bucket: COMPRESSED_BUCKET,
        Key: s3Key,
        Body: fileStream,
        ContentType: 'application/octet-stream',
        Metadata: {
          'original-filename': path.basename(s3Key, '.zmt'),
          'compression-timestamp': Date.now().toString(),
          'file-size': fileSize.toString()
        }
      });

      await s3Client.send(uploadCommand);

      const uploadTime = Date.now() - startTime;
      const uploadSpeed = (fileSize / 1024 / 1024) / (uploadTime / 1000); // MB/s
      console.log(`✅ S3 upload completed: ${s3Key} in ${uploadTime}ms (${uploadSpeed.toFixed(2)} MB/s)`);

    } catch (error) {
      console.error(`❌ S3 upload failed for ${s3Key}:`, error);
      throw new Error(`S3 upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async generateDownloadUrl(s3Key: string): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: COMPRESSED_BUCKET,
      Key: s3Key,
    });

    // Generate presigned URL valid for 7 days
    const downloadUrl = await getSignedUrl(s3Client, command, {
      expiresIn: 7 * 24 * 60 * 60 // 7 days
    });

    return downloadUrl;
  }

  private async cleanup(filePaths: string[]): Promise<void> {
    console.log(`🧹 Cleaning up ${filePaths.length} files`);

    for (const filePath of filePaths) {
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          console.log(`🗑️ Deleted file: ${filePath}`);
        }
      } catch (error) {
        console.error(`❌ Failed to delete ${filePath}:`, error);
      }
    }
  }

  private async cleanupDirectory(dirPath: string): Promise<void> {
    try {
      if (fs.existsSync(dirPath)) {
        fs.rmSync(dirPath, { recursive: true, force: true });
        console.log(`🗑️ Deleted directory: ${dirPath}`);
      }
    } catch (error) {
      console.error(`❌ Failed to delete directory ${dirPath}:`, error);
    }
  }

  getUploadMiddleware() {
    // Create fresh multer instance each time to pick up dynamic file size changes
    return createUpload().single('file');
  }
}

export { EC2UploadService, UploadResult };
