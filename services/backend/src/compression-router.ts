import * as path from 'path';
import * as fs from 'fs-extra';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export interface CompressionResult {
  compressedPath: string;
  compressionTime: number;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  method: string;
}

export interface DecompressionResult {
  extractedFiles: string[];
  decompressionTime: number;
  method: string;
}

export enum CompressionMethod {
  BASIC_ZMT = 'basic_zmt',
  ADVANCED_ZMT = 'advanced_zmt_m5',
  VIDEO_MP4 = 'video_mp4',
  AUDIO = 'audio',
  VIDEO_Y4M = 'video_y4m',
  TIFF_IMAGE = 'tiff_image',
  DICOM = 'dicom'
}

export class CompressionRouter {
  private scriptsDir: string;
  private zmtBinary: string;
  private zmtDcmBinary: string;

  constructor(scriptsDir: string) {
    this.scriptsDir = scriptsDir;
    this.zmtBinary = path.join(scriptsDir, 'zmt');
    this.zmtDcmBinary = path.join(scriptsDir, 'zmtdcm');
  }

  /**
   * Determine compression method based on file extension
   */
  public getCompressionMethod(fileName: string, useAdvanced: boolean = false): CompressionMethod {
    const ext = path.extname(fileName).toLowerCase();
    
    // Basic ZMT extensions
    const basicZmtExtensions = ['.txt', '.pdf', '.xls', '.xlsx', '.doc', '.docx', '.psd', '.csv', '.db', '.ppt', '.pptx'];
    
    // Video extensions for MP4 compression
    const videoMp4Extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'];
    
    // Audio extensions
    const audioExtensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'];
    
    // Y4M video extension
    const y4mExtensions = ['.y4m'];
    
    // TIFF image extensions
    const tiffExtensions = ['.tiff', '.tif'];
    
    // DICOM extensions
    const dicomExtensions = ['.dcm', '.dicom'];

    if (dicomExtensions.includes(ext)) {
      return CompressionMethod.DICOM;
    } else if (tiffExtensions.includes(ext)) {
      return CompressionMethod.TIFF_IMAGE;
    } else if (y4mExtensions.includes(ext)) {
      return CompressionMethod.VIDEO_Y4M;
    } else if (audioExtensions.includes(ext)) {
      return CompressionMethod.AUDIO;
    } else if (videoMp4Extensions.includes(ext)) {
      return CompressionMethod.VIDEO_MP4;
    } else if (basicZmtExtensions.includes(ext)) {
      return useAdvanced ? CompressionMethod.ADVANCED_ZMT : CompressionMethod.BASIC_ZMT;
    } else {
      // Default to basic ZMT for unknown file types
      return useAdvanced ? CompressionMethod.ADVANCED_ZMT : CompressionMethod.BASIC_ZMT;
    }
  }

  /**
   * Compress files using the appropriate method
   */
  public async compressFiles(filePaths: string[], outputPath: string, method?: CompressionMethod): Promise<CompressionResult> {
    const startTime = Date.now();
    
    // If method not specified, determine from first file
    if (!method && filePaths.length > 0) {
      method = this.getCompressionMethod(filePaths[0]);
    }
    
    if (!method) {
      throw new Error('Compression method could not be determined');
    }

    // Calculate original size
    let originalSize = 0;
    for (const filePath of filePaths) {
      const stats = await fs.stat(filePath);
      originalSize += stats.size;
    }

    let compressedPath: string;
    
    switch (method) {
      case CompressionMethod.BASIC_ZMT:
        compressedPath = await this.compressWithBasicZMT(filePaths, outputPath);
        break;
      case CompressionMethod.ADVANCED_ZMT:
        compressedPath = await this.compressWithAdvancedZMT(filePaths, outputPath);
        break;
      case CompressionMethod.VIDEO_MP4:
        compressedPath = await this.compressWithVideoMp4(filePaths, outputPath);
        break;
      case CompressionMethod.AUDIO:
        compressedPath = await this.compressWithAudio(filePaths, outputPath);
        break;
      case CompressionMethod.VIDEO_Y4M:
        compressedPath = await this.compressWithVideoY4M(filePaths, outputPath);
        break;
      case CompressionMethod.TIFF_IMAGE:
        compressedPath = await this.compressWithTiffImage(filePaths, outputPath);
        break;
      case CompressionMethod.DICOM:
        compressedPath = await this.compressWithDicom(filePaths, outputPath);
        break;
      default:
        throw new Error(`Unsupported compression method: ${method}`);
    }

    const compressionTime = Date.now() - startTime;
    
    // Get compressed file size
    const compressedStats = await fs.stat(compressedPath);
    const compressedSize = compressedStats.size;
    // Calculate compression ratio as percentage (ensure it's not negative for small files)
    const compressionRatio = Math.max(0, Math.round(((originalSize - compressedSize) / originalSize) * 100));

    return {
      compressedPath,
      compressionTime,
      originalSize,
      compressedSize,
      compressionRatio,
      method: method.toString()
    };
  }

  /**
   * Extract files using the appropriate method
   */
  public async extractFiles(compressedPath: string, outputDir: string, method: CompressionMethod): Promise<DecompressionResult> {
    const startTime = Date.now();
    
    await fs.ensureDir(outputDir);
    
    let extractedFiles: string[];
    
    switch (method) {
      case CompressionMethod.BASIC_ZMT:
      case CompressionMethod.ADVANCED_ZMT:
      case CompressionMethod.VIDEO_MP4:
      case CompressionMethod.AUDIO:
      case CompressionMethod.VIDEO_Y4M:
      case CompressionMethod.TIFF_IMAGE:
        extractedFiles = await this.extractWithZMT(compressedPath, outputDir);
        break;
      case CompressionMethod.DICOM:
        extractedFiles = await this.extractWithDicom(compressedPath, outputDir);
        break;
      default:
        throw new Error(`Unsupported extraction method: ${method}`);
    }

    const decompressionTime = Date.now() - startTime;

    return {
      extractedFiles,
      decompressionTime,
      method: method.toString()
    };
  }

  /**
   * Basic ZMT compression
   */
  private async compressWithBasicZMT(filePaths: string[], outputPath: string): Promise<string> {
    const workDir = path.dirname(outputPath);
    const outputFileName = path.basename(outputPath);

    // Check if output filename is the same as any input filename (would cause conflict)
    const fileNames = filePaths.map(fp => path.basename(fp));
    const hasNameConflict = fileNames.includes(outputFileName);

    // If there's a name conflict, use a temporary .zmt file and rename it later
    const tempOutputFileName = hasNameConflict ? outputFileName + '.zmt' : outputFileName;
    const tempOutputPath = path.join(workDir, tempOutputFileName);

    const command = `cd "${workDir}" && "${this.zmtBinary}" a "${tempOutputFileName}" ${fileNames.map(f => `"${f}"`).join(' ')}`;

    console.log(`🗜️ Running basic ZMT compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('ZMT compression stderr:', stderr);
    }

    if (!await fs.pathExists(tempOutputPath)) {
      throw new Error('Basic ZMT compression failed - output file not created');
    }

    // If we used a temporary filename, rename it to the desired output
    if (hasNameConflict) {
      await fs.move(tempOutputPath, outputPath, { overwrite: true });
    }

    return outputPath;
  }

  /**
   * Advanced ZMT compression with -m5 mode
   */
  private async compressWithAdvancedZMT(filePaths: string[], outputPath: string): Promise<string> {
    const workDir = path.dirname(outputPath);
    const outputFileName = path.basename(outputPath);
    
    // Create a list of files to compress
    const fileNames = filePaths.map(fp => path.basename(fp));
    const command = `cd "${workDir}" && "${this.zmtBinary}" a -m5 "${outputFileName}" ${fileNames.map(f => `"${f}"`).join(' ')}`;
    
    console.log(`🗜️ Running advanced ZMT compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);
    
    if (stderr) {
      console.log('Advanced ZMT compression stderr:', stderr);
    }
    
    if (!await fs.pathExists(outputPath)) {
      throw new Error('Advanced ZMT compression failed - output file not created');
    }
    
    return outputPath;
  }

  /**
   * Video MP4 compression using FFmpeg
   */
  private async compressWithVideoMp4(filePaths: string[], outputPath: string): Promise<string> {
    if (filePaths.length !== 1) {
      throw new Error('Video compression only supports single file input');
    }

    const inputPath = filePaths[0];
    const inputFileName = path.basename(inputPath);
    const workDir = path.dirname(outputPath);

    // Create required directories
    await fs.ensureDir(path.join(workDir, 'original'));
    await fs.ensureDir(path.join(workDir, 'encoded'));

    // Copy input file to work directory for processing (only if not already there)
    const workInputPath = path.join(workDir, inputFileName);
    if (path.resolve(inputPath) !== path.resolve(workInputPath)) {
      await fs.copy(inputPath, workInputPath);
    }

    // Run the video compression script
    const scriptPath = path.join(this.scriptsDir, 'compress_code_mp4_update.sh');
    const ext = path.extname(inputFileName).substring(1); // Remove the dot
    const command = `cd "${workDir}" && bash "${scriptPath}" "${ext}"`;

    console.log(`🎥 Running video compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('Video compression stderr:', stderr);
    }

    // The script should create a compressed file in the encoded directory
    const encodedDir = path.join(workDir, 'encoded');
    const encodedFiles = await fs.readdir(encodedDir);

    if (encodedFiles.length === 0) {
      throw new Error('Video compression failed - no encoded file created');
    }

    // Move the encoded file to the output path
    const encodedFilePath = path.join(encodedDir, encodedFiles[0]);
    await fs.move(encodedFilePath, outputPath);

    return outputPath;
  }

  /**
   * Audio compression using compress_code_audio.sh script
   */
  private async compressWithAudio(filePaths: string[], outputPath: string): Promise<string> {
    if (filePaths.length !== 1) {
      throw new Error('Audio compression only supports single file input');
    }

    const inputPath = filePaths[0];
    const inputFileName = path.basename(inputPath);
    const workDir = path.dirname(outputPath);

    // Determine output format from the original file extension
    const inputExt = path.extname(inputFileName).toLowerCase().substring(1); // Remove the dot
    const outputFormat = this.getAudioOutputFormat(inputExt);

    // Create required directories
    await fs.ensureDir(path.join(workDir, 'original'));
    await fs.ensureDir(path.join(workDir, 'encoded'));

    // Copy input file to work directory for processing (only if not already there)
    const workInputPath = path.join(workDir, inputFileName);
    if (path.resolve(inputPath) !== path.resolve(workInputPath)) {
      await fs.copy(inputPath, workInputPath);
    }

    // Run the audio compression script
    const scriptPath = path.join(this.scriptsDir, 'compress_code_audio.sh');
    const command = `cd "${workDir}" && bash "${scriptPath}" "${outputFormat}"`;

    console.log(`🎵 Running audio compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('Audio compression stderr:', stderr);
    }

    // The script should create a compressed file in the encoded directory
    const encodedDir = path.join(workDir, 'encoded');
    const encodedFiles = await fs.readdir(encodedDir);

    if (encodedFiles.length === 0) {
      throw new Error('Audio compression failed - no encoded file created');
    }

    // Find the compressed file (should have _zmt suffix)
    const compressedFile = encodedFiles.find(file => file.includes('_zmt'));
    if (!compressedFile) {
      throw new Error('Audio compression failed - compressed file not found');
    }

    // Move the encoded file to the output path
    const encodedFilePath = path.join(encodedDir, compressedFile);
    await fs.move(encodedFilePath, outputPath);

    return outputPath;
  }

  /**
   * Determine the best output format for audio compression based on input format
   */
  private getAudioOutputFormat(inputExtension: string): string {
    // Map input formats to optimal output formats
    const formatMap: { [key: string]: string } = {
      'wav': 'mp3',    // WAV to MP3 for good compression
      'flac': 'mp3',   // FLAC to MP3 for good compression
      'aac': 'aac',    // Keep AAC as AAC
      'ogg': 'mp3',    // OGG to MP3
      'wma': 'mp3',    // WMA to MP3
      'm4a': 'm4a',    // Keep M4A as M4A
      'mp3': 'mp3'     // Keep MP3 as MP3
    };

    return formatMap[inputExtension] || 'mp3'; // Default to MP3
  }

  /**
   * Y4M video compression
   */
  private async compressWithVideoY4M(filePaths: string[], outputPath: string): Promise<string> {
    if (filePaths.length !== 1) {
      throw new Error('Y4M video compression only supports single file input');
    }

    const inputPath = filePaths[0];
    const inputFileName = path.basename(inputPath);
    const workDir = path.dirname(outputPath);

    // Create required directories
    await fs.ensureDir(path.join(workDir, 'original'));
    await fs.ensureDir(path.join(workDir, 'encoded'));

    // Copy input file to work directory for processing (only if not already there)
    const workInputPath = path.join(workDir, inputFileName);
    if (path.resolve(inputPath) !== path.resolve(workInputPath)) {
      await fs.copy(inputPath, workInputPath);
    }

    // Run the Y4M video compression script
    const scriptPath = path.join(this.scriptsDir, 'compress_code_video.sh');
    const command = `cd "${workDir}" && bash "${scriptPath}"`;

    console.log(`🎬 Running Y4M video compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('Y4M video compression stderr:', stderr);
    }

    // The script should create a compressed file in the encoded directory
    const encodedDir = path.join(workDir, 'encoded');
    const encodedFiles = await fs.readdir(encodedDir);

    if (encodedFiles.length === 0) {
      throw new Error('Y4M video compression failed - no encoded file created');
    }

    // Move the encoded file to the output path
    const encodedFilePath = path.join(encodedDir, encodedFiles[0]);
    await fs.move(encodedFilePath, outputPath);

    return outputPath;
  }

  /**
   * TIFF image compression using Python script
   */
  private async compressWithTiffImage(filePaths: string[], outputPath: string): Promise<string> {
    if (filePaths.length !== 1) {
      throw new Error('TIFF image compression only supports single file input');
    }

    const inputPath = filePaths[0];
    const inputFileName = path.basename(inputPath);
    const workDir = path.dirname(outputPath);

    // Copy input file to work directory for processing (only if not already there)
    const workInputPath = path.join(workDir, inputFileName);
    if (path.resolve(inputPath) !== path.resolve(workInputPath)) {
      await fs.copy(inputPath, workInputPath);
    }

    // Run the TIFF compression Python script
    const scriptPath = path.join(this.scriptsDir, 'zmt_image.py');
    const command = `cd "${workDir}" && python3 "${scriptPath}" "${inputFileName}"`;

    console.log(`🖼️ Running TIFF image compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('TIFF compression stderr:', stderr);
    }

    // The script should create a compressed file with .zmt extension
    const compressedFileName = inputFileName + '.zmt';
    const compressedFilePath = path.join(workDir, compressedFileName);

    if (!await fs.pathExists(compressedFilePath)) {
      throw new Error('TIFF compression failed - compressed file not created');
    }

    // Move the compressed file to the output path
    await fs.move(compressedFilePath, outputPath);
    return outputPath;
  }

  /**
   * DICOM compression using zmtdcm binary
   */
  private async compressWithDicom(filePaths: string[], outputPath: string): Promise<string> {
    const workDir = path.dirname(outputPath);
    const outputFileName = path.basename(outputPath);

    // Create a list of files to compress
    const fileNames = filePaths.map(fp => path.basename(fp));
    const command = `cd "${workDir}" && "${this.zmtDcmBinary}" a "${outputFileName}" ${fileNames.map(f => `"${f}"`).join(' ')}`;

    console.log(`🏥 Running DICOM compression: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('DICOM compression stderr:', stderr);
    }

    if (!await fs.pathExists(outputPath)) {
      throw new Error('DICOM compression failed - output file not created');
    }

    return outputPath;
  }

  /**
   * Extract files using ZMT binary
   */
  private async extractWithZMT(compressedPath: string, outputDir: string): Promise<string[]> {
    const command = `cd "${outputDir}" && "${this.zmtBinary}" x "${compressedPath}"`;

    console.log(`📂 Running ZMT extraction: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('ZMT extraction stderr:', stderr);
    }

    // List extracted files
    const files = await fs.readdir(outputDir);
    const extractedFiles = files
      .filter(file => file !== path.basename(compressedPath))
      .map(file => path.join(outputDir, file));

    if (extractedFiles.length === 0) {
      throw new Error('ZMT extraction failed - no files extracted');
    }

    return extractedFiles;
  }

  /**
   * Extract files using DICOM binary
   */
  private async extractWithDicom(compressedPath: string, outputDir: string): Promise<string[]> {
    const command = `cd "${outputDir}" && "${this.zmtDcmBinary}" x "${compressedPath}"`;

    console.log(`🏥 Running DICOM extraction: ${command}`);
    const { stdout, stderr } = await execAsync(command);

    if (stderr) {
      console.log('DICOM extraction stderr:', stderr);
    }

    // List extracted files
    const files = await fs.readdir(outputDir);
    const extractedFiles = files
      .filter(file => file !== path.basename(compressedPath))
      .map(file => path.join(outputDir, file));

    if (extractedFiles.length === 0) {
      throw new Error('DICOM extraction failed - no files extracted');
    }

    return extractedFiles;
  }
}
